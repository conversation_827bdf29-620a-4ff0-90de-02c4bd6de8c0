<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Thank You - MORLA | Booking Confirmed</title>
    <meta name="description" content="Thank you for booking with MORLA. Your transformation journey begins soon.">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="style.css">
    
    <style>
        .thankyou-page {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, var(--dark-wood) 0%, var(--text-dark) 100%);
            position: relative;
            overflow: hidden;
        }
        
        .thankyou-page::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="%23D4AF37" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }
        
        .thankyou-content {
            text-align: center;
            max-width: 600px;
            padding: 3rem 2rem;
            background: rgba(255, 254, 247, 0.95);
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            position: relative;
            z-index: 2;
        }
        
        .success-icon {
            width: 100px;
            height: 100px;
            background: var(--warm-gold);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 2rem;
            animation: pulse 2s infinite;
        }
        
        .success-icon i {
            font-size: 3rem;
            color: var(--ivory-white);
        }
        
        .thankyou-title {
            font-family: var(--font-heading);
            font-size: 2.5rem;
            color: var(--dark-wood);
            margin-bottom: 1rem;
        }
        
        .thankyou-subtitle {
            font-size: 1.2rem;
            color: var(--warm-gold);
            margin-bottom: 2rem;
            font-weight: 600;
        }
        
        .thankyou-message {
            font-size: 1.1rem;
            color: var(--text-light);
            line-height: 1.6;
            margin-bottom: 2rem;
        }
        
        .next-steps {
            background: var(--marble-grey);
            padding: 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            text-align: left;
        }
        
        .next-steps h3 {
            color: var(--dark-wood);
            margin-bottom: 1rem;
            text-align: center;
        }
        
        .step-list {
            list-style: none;
            padding: 0;
        }
        
        .step-list li {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
            color: var(--text-dark);
        }
        
        .step-list li i {
            color: var(--warm-gold);
            margin-right: 1rem;
            width: 20px;
        }
        
        .contact-info {
            background: var(--dark-wood);
            color: var(--ivory-white);
            padding: 1.5rem;
            border-radius: 10px;
            margin-bottom: 2rem;
        }
        
        .contact-info h4 {
            color: var(--warm-gold);
            margin-bottom: 1rem;
        }
        
        .contact-details {
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
            gap: 1rem;
        }
        
        .contact-item {
            text-align: center;
        }
        
        .contact-item i {
            color: var(--warm-gold);
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
        }
        
        .action-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 0.75rem 2rem;
            border: none;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .btn-primary {
            background: var(--warm-gold);
            color: var(--ivory-white);
        }
        
        .btn-primary:hover {
            background: var(--accent-gold);
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background: transparent;
            color: var(--dark-wood);
            border: 2px solid var(--dark-wood);
        }
        
        .btn-secondary:hover {
            background: var(--dark-wood);
            color: var(--ivory-white);
        }
        
        .btn-whatsapp {
            background: #25D366;
            color: white;
        }
        
        .btn-whatsapp:hover {
            background: #128C7E;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        
        @media (max-width: 768px) {
            .thankyou-content {
                margin: 1rem;
                padding: 2rem 1.5rem;
            }
            
            .thankyou-title {
                font-size: 2rem;
            }
            
            .contact-details {
                flex-direction: column;
                gap: 1.5rem;
            }
            
            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="thankyou-page">
        <div class="thankyou-content">
            <div class="success-icon">
                <i class="fas fa-check"></i>
            </div>
            
            <h1 class="thankyou-title">Thank You!</h1>
            <p class="thankyou-subtitle">Your booking has been confirmed</p>
            
            <p class="thankyou-message">
                Thank you for choosing MORLA for your transformation journey. We're excited to help you dress like a boss and feel like a king. You'll be contacted shortly to confirm your appointment details.
            </p>
            
            <div class="next-steps">
                <h3>What Happens Next?</h3>
                <ul class="step-list">
                    <li>
                        <i class="fas fa-phone"></i>
                        Our team will call you within 24 hours to confirm your appointment
                    </li>
                    <li>
                        <i class="fas fa-calendar-alt"></i>
                        We'll discuss your preferences and any special requirements
                    </li>
                    <li>
                        <i class="fas fa-clock"></i>
                        Arrive 15 minutes early for your appointment
                    </li>
                    <li>
                        <i class="fas fa-crown"></i>
                        Enjoy your premium MORLA experience
                    </li>
                </ul>
            </div>
            
            <div class="contact-info">
                <h4>Need to reach us?</h4>
                <div class="contact-details">
                    <div class="contact-item">
                        <i class="fas fa-phone"></i>
                        <p>+252 123 456 789</p>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-envelope"></i>
                        <p><EMAIL></p>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <p>Hargeisa, Somalia</p>
                    </div>
                </div>
            </div>
            
            <div class="action-buttons">
                <a href="index.html" class="btn btn-primary">
                    <i class="fas fa-home"></i>
                    Back to Home
                </a>
                <a href="https://wa.me/252123456789?text=Hello%20MORLA,%20I%20just%20booked%20an%20appointment%20and%20have%20a%20question." class="btn btn-whatsapp" target="_blank">
                    <i class="fab fa-whatsapp"></i>
                    WhatsApp Us
                </a>
                <a href="index.html#gallery" class="btn btn-secondary">
                    <i class="fas fa-images"></i>
                    View Gallery
                </a>
            </div>
        </div>
    </div>
    
    <script>
        // Add some interactive elements
        document.addEventListener('DOMContentLoaded', function() {
            // Animate the success icon
            const successIcon = document.querySelector('.success-icon');
            setTimeout(() => {
                successIcon.style.transform = 'scale(1.1)';
                setTimeout(() => {
                    successIcon.style.transform = 'scale(1)';
                }, 200);
            }, 500);
            
            // Add click tracking for buttons
            document.querySelectorAll('.btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    console.log('Button clicked:', this.textContent.trim());
                });
            });
        });
    </script>
</body>
</html>
