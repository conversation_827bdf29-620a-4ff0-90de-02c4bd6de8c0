// ===== GLOBAL VARIABLES =====
let cart = JSON.parse(localStorage.getItem('morlaCart')) || [];

// Fashion products data
const fashionData = {
    suits: [
        { id: 1, name: "Executive Black Suit", price: "$899", image: "https://via.placeholder.com/300x400/2C1810/FFFFFF?text=Black+Suit" },
        { id: 2, name: "Navy Blue Premium", price: "$799", image: "https://via.placeholder.com/300x400/2C1810/FFFFFF?text=Navy+Suit" },
        { id: 3, name: "Charcoal Grey Elite", price: "$849", image: "https://via.placeholder.com/300x400/2C1810/FFFFFF?text=Grey+Suit" },
        { id: 4, name: "Royal Blue Boss", price: "$899", image: "https://via.placeholder.com/300x400/2C1810/FFFFFF?text=Royal+Suit" }
    ],
    shoes: [
        { id: 5, name: "Oxford Leather Brown", price: "$299", image: "https://via.placeholder.com/300x400/8B4513/FFFFFF?text=Oxford+Shoes" },
        { id: 6, name: "Derby Black Classic", price: "$279", image: "https://via.placeholder.com/300x400/8B4513/FFFFFF?text=Derby+Shoes" },
        { id: 7, name: "Loafers Premium", price: "$249", image: "https://via.placeholder.com/300x400/8B4513/FFFFFF?text=Loafers" },
        { id: 8, name: "Brogues Handcrafted", price: "$329", image: "https://via.placeholder.com/300x400/8B4513/FFFFFF?text=Brogues" }
    ],
    glasses: [
        { id: 9, name: "Aviator Gold Frame", price: "$199", image: "https://via.placeholder.com/300x400/D4AF37/FFFFFF?text=Aviator" },
        { id: 10, name: "Square Black Frame", price: "$179", image: "https://via.placeholder.com/300x400/D4AF37/FFFFFF?text=Square" },
        { id: 11, name: "Round Vintage", price: "$159", image: "https://via.placeholder.com/300x400/D4AF37/FFFFFF?text=Round" },
        { id: 12, name: "Wayfarer Classic", price: "$189", image: "https://via.placeholder.com/300x400/D4AF37/FFFFFF?text=Wayfarer" }
    ],
    trousers: [
        { id: 13, name: "Slim Fit Chinos", price: "$129", image: "https://via.placeholder.com/300x400/6D4C41/FFFFFF?text=Chinos" },
        { id: 14, name: "Formal Black Pants", price: "$149", image: "https://via.placeholder.com/300x400/6D4C41/FFFFFF?text=Formal" },
        { id: 15, name: "Casual Khaki", price: "$119", image: "https://via.placeholder.com/300x400/6D4C41/FFFFFF?text=Khaki" },
        { id: 16, name: "Dress Pants Grey", price: "$139", image: "https://via.placeholder.com/300x400/6D4C41/FFFFFF?text=Dress" }
    ],
    jackets: [
        { id: 17, name: "Blazer Navy Blue", price: "$399", image: "https://via.placeholder.com/300x400/3E2723/FFFFFF?text=Blazer" },
        { id: 18, name: "Leather Jacket Black", price: "$599", image: "https://via.placeholder.com/300x400/3E2723/FFFFFF?text=Leather" },
        { id: 19, name: "Bomber Jacket", price: "$299", image: "https://via.placeholder.com/300x400/3E2723/FFFFFF?text=Bomber" },
        { id: 20, name: "Wool Coat Grey", price: "$499", image: "https://via.placeholder.com/300x400/3E2723/FFFFFF?text=Wool+Coat" }
    ],
    perfumes: [
        { id: 21, name: "MORLA Signature", price: "$89", image: "https://via.placeholder.com/300x400/D4AF37/FFFFFF?text=Signature" },
        { id: 22, name: "Boss Essence", price: "$79", image: "https://via.placeholder.com/300x400/D4AF37/FFFFFF?text=Boss" },
        { id: 23, name: "Royal Scent", price: "$99", image: "https://via.placeholder.com/300x400/D4AF37/FFFFFF?text=Royal" },
        { id: 24, name: "Executive Cologne", price: "$69", image: "https://via.placeholder.com/300x400/D4AF37/FFFFFF?text=Executive" }
    ]
};

// ===== DOM CONTENT LOADED =====
document.addEventListener('DOMContentLoaded', function() {
    // Initialize AOS
    AOS.init({
        duration: 1000,
        once: true,
        offset: 100
    });

    // Hide loading screen
    setTimeout(() => {
        const loadingScreen = document.getElementById('loading-screen');
        loadingScreen.style.opacity = '0';
        setTimeout(() => {
            loadingScreen.style.display = 'none';
        }, 500);
    }, 2000);

    // Initialize all functionality
    initNavigation();
    initFashionTabs();
    initCart();
    initScrollEffects();
    updateCartDisplay();
});

// ===== NAVIGATION =====
function initNavigation() {
    const hamburger = document.getElementById('hamburger');
    const navMenu = document.getElementById('nav-menu');
    const navbar = document.getElementById('navbar');

    // Mobile menu toggle
    hamburger.addEventListener('click', () => {
        hamburger.classList.toggle('active');
        navMenu.classList.toggle('active');
    });

    // Close mobile menu when clicking on a link
    document.querySelectorAll('.nav-link').forEach(link => {
        link.addEventListener('click', () => {
            hamburger.classList.remove('active');
            navMenu.classList.remove('active');
        });
    });

    // Navbar scroll effect
    window.addEventListener('scroll', () => {
        if (window.scrollY > 100) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    });

    // Smooth scrolling for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// ===== FASHION TABS =====
function initFashionTabs() {
    const tabButtons = document.querySelectorAll('.tab-btn');
    const fashionContent = document.getElementById('fashion-content');

    // Set initial active tab
    displayFashionCategory('suits');

    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            // Remove active class from all buttons
            tabButtons.forEach(btn => btn.classList.remove('active'));
            // Add active class to clicked button
            button.classList.add('active');
            
            // Display corresponding category
            const category = button.getAttribute('data-tab');
            displayFashionCategory(category);
        });
    });
}

function displayFashionCategory(category) {
    const fashionContent = document.getElementById('fashion-content');
    const products = fashionData[category];

    if (!products) return;

    const html = `
        <div class="product-grid">
            ${products.map(product => `
                <div class="product-card" data-aos="fade-up">
                    <div class="product-image">
                        <img src="${product.image}" alt="${product.name}" loading="lazy">
                        <div class="product-overlay">
                            <button class="add-to-cart-btn" onclick="addToCart(${product.id}, '${category}')">
                                <i class="fas fa-shopping-bag"></i> Add to Cart
                            </button>
                        </div>
                    </div>
                    <div class="product-info">
                        <h3>${product.name}</h3>
                        <p class="product-price">${product.price}</p>
                        <button class="whatsapp-single" onclick="orderSingle(${product.id}, '${category}')">
                            <i class="fab fa-whatsapp"></i> Order on WhatsApp
                        </button>
                    </div>
                </div>
            `).join('')}
        </div>
    `;

    fashionContent.innerHTML = html;
    
    // Refresh AOS for new elements
    AOS.refresh();
}

// ===== CART FUNCTIONALITY =====
function initCart() {
    const cartToggle = document.getElementById('cart-toggle');
    const cartSidebar = document.getElementById('cart-sidebar');
    const closeCart = document.getElementById('close-cart');
    const whatsappOrder = document.getElementById('whatsapp-order');

    cartToggle.addEventListener('click', () => {
        cartSidebar.classList.add('active');
    });

    closeCart.addEventListener('click', () => {
        cartSidebar.classList.remove('active');
    });

    // Close cart when clicking outside
    document.addEventListener('click', (e) => {
        if (!cartSidebar.contains(e.target) && !cartToggle.contains(e.target)) {
            cartSidebar.classList.remove('active');
        }
    });

    whatsappOrder.addEventListener('click', orderOnWhatsApp);
}

function addToCart(productId, category) {
    const product = fashionData[category].find(p => p.id === productId);
    if (!product) return;

    const existingItem = cart.find(item => item.id === productId);
    
    if (existingItem) {
        existingItem.quantity += 1;
    } else {
        cart.push({
            id: productId,
            name: product.name,
            price: product.price,
            category: category,
            quantity: 1
        });
    }

    localStorage.setItem('morlaCart', JSON.stringify(cart));
    updateCartDisplay();
    
    // Show success message
    showNotification('Item added to cart!');
}

function removeFromCart(productId) {
    cart = cart.filter(item => item.id !== productId);
    localStorage.setItem('morlaCart', JSON.stringify(cart));
    updateCartDisplay();
}

function updateCartQuantity(productId, change) {
    const item = cart.find(item => item.id === productId);
    if (item) {
        item.quantity += change;
        if (item.quantity <= 0) {
            removeFromCart(productId);
        } else {
            localStorage.setItem('morlaCart', JSON.stringify(cart));
            updateCartDisplay();
        }
    }
}

function updateCartDisplay() {
    const cartCount = document.getElementById('cart-count');
    const cartItems = document.getElementById('cart-items');

    // Update cart count
    const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
    cartCount.textContent = totalItems;
    cartCount.style.display = totalItems > 0 ? 'block' : 'none';

    // Update cart items
    if (cart.length === 0) {
        cartItems.innerHTML = '<p class="empty-cart">Your cart is empty</p>';
        return;
    }

    const html = cart.map(item => `
        <div class="cart-item">
            <div class="cart-item-info">
                <h4>${item.name}</h4>
                <p>${item.price}</p>
            </div>
            <div class="cart-item-controls">
                <button onclick="updateCartQuantity(${item.id}, -1)">-</button>
                <span>${item.quantity}</span>
                <button onclick="updateCartQuantity(${item.id}, 1)">+</button>
                <button onclick="removeFromCart(${item.id})" class="remove-btn">&times;</button>
            </div>
        </div>
    `).join('');

    cartItems.innerHTML = html;
}

function orderSingle(productId, category) {
    const product = fashionData[category].find(p => p.id === productId);
    if (!product) return;

    const message = `Hello MORLA, I'd like to order:\n- ${product.name} (${product.price})`;
    const whatsappUrl = `https://wa.me/252123456789?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
}

function orderOnWhatsApp() {
    if (cart.length === 0) {
        showNotification('Your cart is empty!');
        return;
    }

    let message = "Hello MORLA, I'd like to order:\n";
    cart.forEach(item => {
        message += `- ${item.name} (${item.price}) x${item.quantity}\n`;
    });

    const whatsappUrl = `https://wa.me/252123456789?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
}

// ===== SCROLL EFFECTS =====
function initScrollEffects() {
    // Scroll to top button
    const scrollTopBtn = document.createElement('button');
    scrollTopBtn.innerHTML = '<i class="fas fa-arrow-up"></i>';
    scrollTopBtn.className = 'scroll-top-btn';
    scrollTopBtn.style.cssText = `
        position: fixed;
        bottom: 30px;
        right: 30px;
        width: 50px;
        height: 50px;
        background: var(--warm-gold);
        color: white;
        border: none;
        border-radius: 50%;
        cursor: pointer;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        z-index: 1000;
        font-size: 1.2rem;
    `;
    
    document.body.appendChild(scrollTopBtn);

    // Show/hide scroll to top button
    window.addEventListener('scroll', () => {
        if (window.scrollY > 500) {
            scrollTopBtn.style.opacity = '1';
            scrollTopBtn.style.visibility = 'visible';
        } else {
            scrollTopBtn.style.opacity = '0';
            scrollTopBtn.style.visibility = 'hidden';
        }
    });

    scrollTopBtn.addEventListener('click', () => {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
}

// ===== UTILITY FUNCTIONS =====
function showNotification(message) {
    const notification = document.createElement('div');
    notification.textContent = message;
    notification.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        background: var(--warm-gold);
        color: white;
        padding: 1rem 2rem;
        border-radius: 5px;
        z-index: 10000;
        animation: slideIn 0.3s ease;
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.remove();
    }, 3000);
}

// Add CSS for animations and additional styles
const additionalStyles = `
    @keyframes slideIn {
        from { transform: translateX(100%); }
        to { transform: translateX(0); }
    }

    .product-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 2rem;
        margin-top: 2rem;
    }

    .product-card {
        background: white;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        transition: transform 0.3s ease;
    }

    .product-card:hover {
        transform: translateY(-5px);
    }

    .product-image {
        position: relative;
        overflow: hidden;
    }

    .product-image img {
        width: 100%;
        height: 300px;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .product-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(212, 175, 55, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .product-card:hover .product-overlay {
        opacity: 1;
    }

    .add-to-cart-btn {
        background: white;
        color: var(--dark-wood);
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 5px;
        cursor: pointer;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .add-to-cart-btn:hover {
        transform: scale(1.05);
    }

    .product-info {
        padding: 1.5rem;
        text-align: center;
    }

    .product-info h3 {
        color: var(--dark-wood);
        margin-bottom: 0.5rem;
    }

    .product-price {
        font-size: 1.2rem;
        font-weight: 600;
        color: var(--warm-gold);
        margin-bottom: 1rem;
    }

    .whatsapp-single {
        background: #25D366;
        color: white;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 5px;
        cursor: pointer;
        font-size: 0.9rem;
        transition: all 0.3s ease;
    }

    .whatsapp-single:hover {
        background: #128C7E;
    }

    .cart-sidebar {
        position: fixed;
        top: 0;
        right: -400px;
        width: 400px;
        height: 100vh;
        background: white;
        box-shadow: -5px 0 15px rgba(0,0,0,0.1);
        transition: right 0.3s ease;
        z-index: 10000;
        display: flex;
        flex-direction: column;
    }

    .cart-sidebar.active {
        right: 0;
    }

    .cart-header {
        padding: 1.5rem;
        border-bottom: 1px solid #eee;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .close-cart {
        background: none;
        border: none;
        font-size: 1.5rem;
        cursor: pointer;
        color: var(--dark-wood);
    }

    .cart-items {
        flex: 1;
        padding: 1rem;
        overflow-y: auto;
    }

    .cart-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem 0;
        border-bottom: 1px solid #eee;
    }

    .cart-item-controls {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .cart-item-controls button {
        width: 30px;
        height: 30px;
        border: 1px solid #ddd;
        background: white;
        cursor: pointer;
        border-radius: 3px;
    }

    .remove-btn {
        background: #ff4444 !important;
        color: white !important;
        border: none !important;
    }

    .cart-footer {
        padding: 1.5rem;
        border-top: 1px solid #eee;
    }

    .whatsapp-btn {
        width: 100%;
        background: #25D366;
        color: white;
        border: none;
        padding: 1rem;
        border-radius: 5px;
        cursor: pointer;
        font-size: 1rem;
        font-weight: 600;
    }

    .cart-toggle {
        position: fixed;
        bottom: 100px;
        right: 30px;
        width: 60px;
        height: 60px;
        background: var(--warm-gold);
        color: white;
        border: none;
        border-radius: 50%;
        cursor: pointer;
        font-size: 1.5rem;
        z-index: 1000;
        transition: all 0.3s ease;
    }

    .cart-toggle:hover {
        transform: scale(1.1);
    }

    .cart-count {
        position: absolute;
        top: -5px;
        right: -5px;
        background: #ff4444;
        color: white;
        border-radius: 50%;
        width: 25px;
        height: 25px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .empty-cart {
        text-align: center;
        color: #999;
        padding: 2rem;
    }

    @media (max-width: 768px) {
        .cart-sidebar {
            width: 100%;
            right: -100%;
        }
        
        .product-grid {
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }
    }
`;

// Inject additional styles
const styleSheet = document.createElement('style');
styleSheet.textContent = additionalStyles;
document.head.appendChild(styleSheet);
