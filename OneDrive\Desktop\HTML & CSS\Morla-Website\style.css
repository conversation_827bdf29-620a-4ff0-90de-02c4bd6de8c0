/* ===== CSS VARIABLES ===== */
:root {
    /* Colors based on reception theme */
    --ivory-white: #FFFEF7;
    --warm-gold: #D4AF37;
    --dark-wood: #3E2723;
    --marble-grey: #E8E8E8;
    --accent-gold: #FFD700;
    --text-dark: #2C1810;
    --text-light: #6D4C41;
    --shadow-light: rgba(212, 175, 55, 0.1);
    --shadow-dark: rgba(62, 39, 35, 0.2);
    
    /* Typography */
    --font-heading: 'Playfair Display', serif;
    --font-body: 'Poppins', sans-serif;
    
    /* Spacing */
    --section-padding: 80px 0;
    --container-padding: 0 20px;
    --border-radius: 8px;
    
    /* Transitions */
    --transition: all 0.3s ease;
}

/* ===== RESET & BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-body);
    line-height: 1.6;
    color: var(--text-dark);
    background-color: var(--ivory-white);
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--container-padding);
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-heading);
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 1rem;
}

h1 { font-size: clamp(2.5rem, 5vw, 4rem); }
h2 { font-size: clamp(2rem, 4vw, 3rem); }
h3 { font-size: clamp(1.5rem, 3vw, 2rem); }

p {
    margin-bottom: 1rem;
    color: var(--text-light);
}

/* ===== LOADING SCREEN ===== */
#loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--dark-wood);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.5s ease;
}

.loader {
    text-align: center;
}

.logo-loading {
    font-family: var(--font-heading);
    font-size: 3rem;
    color: var(--warm-gold);
    letter-spacing: 0.2em;
    margin-bottom: 2rem;
    animation: pulse 2s infinite;
}

.loading-bar {
    width: 200px;
    height: 4px;
    background: var(--marble-grey);
    border-radius: 2px;
    overflow: hidden;
    position: relative;
}

.loading-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--warm-gold);
    animation: loading 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* ===== NAVIGATION ===== */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 254, 247, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    transition: var(--transition);
    border-bottom: 1px solid var(--marble-grey);
}

.navbar.scrolled {
    background: rgba(255, 254, 247, 0.98);
    box-shadow: 0 2px 20px var(--shadow-light);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 80px;
}

.nav-logo h1 {
    font-family: var(--font-heading);
    font-size: 2rem;
    color: var(--dark-wood);
    letter-spacing: 0.1em;
    margin: 0;
}

.nav-menu {
    display: flex;
    list-style: none;
    align-items: center;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: var(--text-dark);
    font-weight: 500;
    transition: var(--transition);
    position: relative;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav-link:hover {
    color: var(--warm-gold);
}

.nav-link.cta-nav {
    background: var(--warm-gold);
    color: var(--ivory-white);
    padding: 0.5rem 1.5rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.nav-link.cta-nav:hover {
    background: var(--accent-gold);
    transform: translateY(-2px);
}

/* Dropdown Menu */
.dropdown {
    position: relative;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: var(--ivory-white);
    min-width: 180px;
    box-shadow: 0 8px 25px var(--shadow-dark);
    border-radius: var(--border-radius);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition);
    list-style: none;
    padding: 1rem 0;
    margin-top: 1rem;
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu li {
    padding: 0;
}

.dropdown-menu a {
    display: block;
    padding: 0.75rem 1.5rem;
    color: var(--text-dark);
    text-decoration: none;
    transition: var(--transition);
}

.dropdown-menu a:hover {
    background: var(--marble-grey);
    color: var(--warm-gold);
}

/* Mobile Menu */
.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.bar {
    width: 25px;
    height: 3px;
    background: var(--dark-wood);
    transition: var(--transition);
}

/* ===== HERO SECTION ===== */
.hero {
    height: 100vh;
    background: linear-gradient(135deg, var(--dark-wood) 0%, var(--text-dark) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="%23D4AF37" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(62, 39, 35, 0.4);
}

.hero-content {
    position: relative;
    z-index: 2;
    max-width: 800px;
    padding: 0 20px;
}

.hero-logo h1 {
    font-size: clamp(3rem, 8vw, 6rem);
    color: var(--warm-gold);
    letter-spacing: 0.2em;
    margin-bottom: 0.5rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.hero-subtitle {
    font-size: 1.2rem;
    color: var(--marble-grey);
    margin-bottom: 2rem;
    letter-spacing: 0.1em;
}

.hero-tagline {
    font-size: clamp(2rem, 5vw, 3.5rem);
    color: var(--ivory-white);
    margin-bottom: 1.5rem;
    font-weight: 400;
}

.hero-description {
    font-size: 1.1rem;
    color: var(--marble-grey);
    margin-bottom: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-button {
    display: inline-block;
    background: var(--warm-gold);
    color: var(--ivory-white);
    padding: 1rem 2.5rem;
    text-decoration: none;
    border-radius: var(--border-radius);
    font-weight: 600;
    font-size: 1.1rem;
    transition: var(--transition);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border: 2px solid var(--warm-gold);
}

.cta-button:hover {
    background: transparent;
    color: var(--warm-gold);
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(212, 175, 55, 0.3);
}

.scroll-indicator {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    animation: bounce 2s infinite;
}

.scroll-arrow {
    width: 2px;
    height: 30px;
    background: var(--warm-gold);
    position: relative;
}

.scroll-arrow::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: -4px;
    width: 10px;
    height: 10px;
    border-right: 2px solid var(--warm-gold);
    border-bottom: 2px solid var(--warm-gold);
    transform: rotate(45deg);
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateX(-50%) translateY(0); }
    40% { transform: translateX(-50%) translateY(-10px); }
    60% { transform: translateX(-50%) translateY(-5px); }
}

/* ===== SECTION STYLES ===== */
.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title {
    color: var(--dark-wood);
    margin-bottom: 1rem;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: var(--warm-gold);
}

.section-subtitle {
    font-size: 1.1rem;
    color: var(--text-light);
    font-style: italic;
}

/* ===== ABOUT SECTION ===== */
.about-section {
    padding: var(--section-padding);
    background: var(--ivory-white);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-intro {
    font-size: 1.2rem;
    color: var(--text-dark);
    margin-bottom: 2rem;
}

.founder-quote {
    background: var(--marble-grey);
    padding: 2rem;
    border-left: 4px solid var(--warm-gold);
    margin: 2rem 0;
    border-radius: var(--border-radius);
}

.founder-quote p {
    font-size: 1.3rem;
    font-style: italic;
    color: var(--dark-wood);
    margin-bottom: 1rem;
}

.founder-quote cite {
    color: var(--warm-gold);
    font-weight: 600;
}

.image-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.image-item {
    text-align: center;
}

.image-item img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.image-item img:hover {
    transform: scale(1.05);
}

.image-item p {
    margin-top: 1rem;
    font-weight: 600;
    color: var(--dark-wood);
}

/* ===== EXPERIENCE SECTION ===== */
.experience-section {
    padding: var(--section-padding);
    background: var(--marble-grey);
}

.experience-steps {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    gap: 2rem;
    margin-bottom: 3rem;
}

.step {
    text-align: center;
    max-width: 200px;
}

.step-icon {
    width: 80px;
    height: 80px;
    background: var(--warm-gold);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    transition: var(--transition);
}

.step-icon:hover {
    transform: scale(1.1);
    background: var(--accent-gold);
}

.step-icon i {
    font-size: 2rem;
    color: var(--ivory-white);
}

.step h3 {
    color: var(--dark-wood);
    margin-bottom: 0.5rem;
}

.step p {
    font-size: 0.9rem;
    color: var(--text-light);
}

.step-arrow {
    font-size: 1.5rem;
    color: var(--warm-gold);
}

.experience-cta {
    text-align: center;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 80px;
        flex-direction: column;
        background: var(--ivory-white);
        width: 100%;
        text-align: center;
        transition: var(--transition);
        box-shadow: 0 10px 27px var(--shadow-dark);
        padding: 2rem 0;
        gap: 1rem;
    }
    
    .nav-menu.active {
        left: 0;
    }
    
    .hamburger {
        display: flex;
    }
    
    .hamburger.active .bar:nth-child(2) {
        opacity: 0;
    }
    
    .hamburger.active .bar:nth-child(1) {
        transform: translateY(7px) rotate(45deg);
    }
    
    .hamburger.active .bar:nth-child(3) {
        transform: translateY(-7px) rotate(-45deg);
    }
    
    .about-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .experience-steps {
        flex-direction: column;
    }
    
    .step-arrow {
        transform: rotate(90deg);
    }
    
    .image-grid {
        grid-template-columns: 1fr;
    }
}
